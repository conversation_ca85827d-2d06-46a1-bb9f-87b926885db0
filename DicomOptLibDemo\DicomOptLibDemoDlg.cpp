﻿
// DicomOptLibDemoDlg.cpp: 实现文件
//

#include "pch.h"
#include "framework.h"
#include "DicomOptLibDemo.h"
#include "DicomOptLibDemoDlg.h"
#include "afxdialogex.h"



#ifdef _DEBUG
#define new DEBUG_NEW
#endif

#pragma  comment (lib, "../x64/Debug/DicomOptLib.lib")
#include "../Depends/Common/include/LPMCommonLib.h"

#pragma  comment (lib, "../Depends/Common/lib/LPMCommonLib.lib")
// 用于应用程序“关于”菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_ABOUTBOX };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

// 实现
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialogEx(IDD_ABOUTBOX)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()


// CDicomOptLibDemoDlg 对话框



CDicomOptLibDemoDlg::CDicomOptLibDemoDlg(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_DICOMOPTLIBDEMO_DIALOG, pParent)
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

CDicomOptLibDemoDlg::~CDicomOptLibDemoDlg()
{
	if (m_pStore)
	{
		delete m_pStore;
	}

	if (m_pFind)
	{
		delete m_pFind;
	}
}

void CDicomOptLibDemoDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CDicomOptLibDemoDlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_BN_CLICKED(ID_BtnFindConnect, &CDicomOptLibDemoDlg::OnBnClickedBtnfindconnect)
	ON_BN_CLICKED(ID_Btn_Find, &CDicomOptLibDemoDlg::OnBnClickedBtnFind)
	ON_BN_CLICKED(ID_BtnFindDisconnect, &CDicomOptLibDemoDlg::OnBnClickedBtnfinddisconnect)
	
	ON_BN_CLICKED(ID_BtnStoreConnect, &CDicomOptLibDemoDlg::OnBnClickedBtnstoreconnect)
	ON_BN_CLICKED(ID_BtnStore, &CDicomOptLibDemoDlg::OnBnClickedBtnstore)
	ON_BN_CLICKED(ID_BtnStoreDis, &CDicomOptLibDemoDlg::OnBnClickedBtnstoredis)
	ON_BN_CLICKED(ID_BtnDicomToJpg, &CDicomOptLibDemoDlg::OnBnClickedBtndicomtojpg)
	ON_BN_CLICKED(ID_BtnWorkListQuery, &CDicomOptLibDemoDlg::OnBnClickedBtnworklistquery)
	ON_BN_CLICKED(ID_BtnReadDcmInfo, &CDicomOptLibDemoDlg::OnBnClickedBtnreaddcminfo)
	ON_BN_CLICKED(IDC_BUTTON1, &CDicomOptLibDemoDlg::OnBnClickedButton1)
	ON_BN_CLICKED(ID_BtnDicomScpBegin, &CDicomOptLibDemoDlg::OnBnClickedBtndicomscpbegin)
	ON_BN_CLICKED(ID_BtnDicomEchoScu, &CDicomOptLibDemoDlg::OnBnClickedBtndicomechoscu)
	ON_BN_CLICKED(IDOK, &CDicomOptLibDemoDlg::OnBnClickedOk)
	ON_BN_CLICKED(ID_BtnDicomScpStop, &CDicomOptLibDemoDlg::OnBnClickedBtndicomscpstop)
	ON_BN_CLICKED(ID_BtnCreateMerDcmFile, &CDicomOptLibDemoDlg::OnBnClickedBtncreatemerdcmfile)
	ON_BN_CLICKED(ID_BtnModifyMerDcmFile, &CDicomOptLibDemoDlg::OnBnClickedBtnmodifymerdcmfile)
	ON_BN_CLICKED(ID_BtnCreateDicomPic, &CDicomOptLibDemoDlg::OnBnClickedBtncreatedicompic)
	ON_BN_CLICKED(ID_BtnModifyPicInfo, &CDicomOptLibDemoDlg::OnBnClickedBtnmodifypicinfo)
END_MESSAGE_MAP()


// CDicomOptLibDemoDlg 消息处理程序

BOOL CDicomOptLibDemoDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();


	time_t t = 1647248108 + 8 * 3600;
	struct tm p;
	gmtime_s(&p, &t);
	char s[80];
	strftime(s, 80, "%Y-%m-%d %H:%M:%S", &p);

	char s1[80] = {};
	strftime(s1, 80, "%Y%m%d", &p);
	char s2[80] = {};
	strftime(s2, 80, "%H%M%S", &p);
	printf("%d: %s\n", (int)t, s);

	// 将“关于...”菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != nullptr)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}
	LPM::SetLogPath("", "dicom");
	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	// TODO: 在此添加额外的初始化代码

	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

void CDicomOptLibDemoDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。  对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CDicomOptLibDemoDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);

		
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CDicomOptLibDemoDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}




#include<io.h>
#include<iostream>
static void searchChild(const std::string& dir, std::vector<std::string>& folders, std::vector<std::string>& files,std::vector<std::string> = {})
{
	struct _finddata_t file;
	std::string path = dir + "/*";
	auto h = _findfirst(path.c_str(), &file);
	if (h == -1) 
	{
		return;
	}

	do {
		if (0 == strncmp(file.name, ".", 1) || 0 == strncmp(file.name, "..", 2)) 
		{
			continue;
		}
		if (file.attrib == _A_SUBDIR) 
		{
			folders.push_back(dir + "/" +file.name);
		}
		else
		{
			files.push_back(dir + "/" + file.name);
		}
	} while (!_findnext(h,&file));

	_findclose(h);
}

static void searchdir(const std::string &dir)
{
	std::vector<std::string> files;
	std::vector<std::string> folders;
	searchChild(dir, folders, files);

	std::vector<std::string> files2;
	std::vector<std::string> folders2;

	folders2 = folders;
	while (folders2.size())
	{
		std::vector<std::string> folders3;
		for (const auto& foledr : folders2)
		{
			std::vector<std::string> tfiles;
			std::vector<std::string> tfolders;
			searchChild(foledr, tfolders, tfiles);
			files.insert(files.end(),tfiles.begin(), tfiles.end());
			folders3.insert(folders3.end(),tfolders.begin(), tfolders.end());
		}

		folders2 = folders3;
	}

	int i = 0;
}

static void main_2()
{
	searchdir("E:/work/AyjPacisSdkLib");
}
#if 0
void CDicomOptLibDemoDlg::OnBnClickedBtnfindconnect()
{
	std::thread t([&]
	{

		//searchdir("E:/work/AyjPacisSdkLib");
		//return;
		if (nullptr == m_pGet)
		{
			m_pGet = new LPM::CDcmCGetScu();
			//m_pFind->InitParam("192.168.0.142", 104, "StartPacs", "AyjPaics");

			m_pGet->InitParam("120.78.215.102", 104, "StartPacs", "AyjPaics2");
		}

	});
	t.detach();
	
}

void CDicomOptLibDemoDlg::OnBnClickedBtnFind()
{
	if (m_pGet)
	{
		std::vector<tagPatientCaseInfo> vectFindCase;
		tagPatientCaseInfo tag;
		tag.strPatientName = "张斐";
		tag.strStudyDate = "20240202";
		tag.strStudyUID = "US2007220056_1.2.156.600734.**********.20200803.1596420751243";
	    //vectFindCase.push_back(tag);

		tagPatientCaseInfo tag1;
		tag1.strPatientName = "刘帆-20240223";
		tag1.strStudyDate = "20240223";
		tag1.strStudyUID = "1.2.156.600734.1105728.20131107.13378984";
		vectFindCase.push_back(tag1);

		m_pGet->GetScu(vectFindCase,"E:/case/WorkstationImage");
	}
}

#else 
void CDicomOptLibDemoDlg::OnBnClickedBtnfindconnect()
{
	std::thread t([&]
	{
		if (nullptr == m_pFind)
		{
			m_pFind = new LPM::CDcmCFindScu();
			//m_pFind->InitParam("192.168.0.142", 104, "StartPacs", "AyjPaics");
		}
	});
	t.detach();

}

void CDicomOptLibDemoDlg::OnBnClickedBtnFind()
{

	if (m_pFind)
	{
		int ret = m_pFind->InitParam("120.78.215.102", 10, "StartPacs", "AyjPaics2");
		if (ret!=0)
		{
			return ;
		}
		//m_pFind->InitParam("127.0.0.2", 1040, "StartPacs", "AyjPaics2");
		std::vector<tagPatientCaseInfo> vectFindCase;
		m_pFind->FindScu("20240323", "20240323", vectFindCase);
	}
}
#endif




void CDicomOptLibDemoDlg::OnBnClickedBtnfinddisconnect()
{
	if (m_pFind)
	{
		delete m_pFind;
		m_pFind = nullptr;
	}
}


void CDicomOptLibDemoDlg::OnBnClickedBtnstoreconnect()
{
	if (nullptr == m_pStore)
	{
		//http://***********:8104/pacsAI
		m_pStore = new LPM::CDcmStoreScu();
		//m_pStore->InitParam("************", 9090, "ayjCenter", "AyjPaics");

		m_pStore->InitParam("*************", 110, "VP", "SUPAICS");
	}
}


void CDicomOptLibDemoDlg::OnBnClickedBtnstore()
{
	if (m_pStore)
	{
		tagPatientShotPicInfo tagInfo;
		tagInfo.llCheckTimeStamp = time(nullptr);
		tagInfo.nPatientSex = 2;
		tagInfo.nPatientAge = 29;
		//tagInfo.strAccessionNo = "1.2.156.600734.**********.20140409.8811906.001";
	
		
		tagInfo.strPicDescription = u8"经丘脑横切面";
		tagInfo.strPicId = "**********";
		tagInfo.strStudyDate = "03";
		tagInfo.strPatLocalId = "20250707001";//	外部id
		tagInfo.strPatientName = "Mike^Test";
		tagInfo.strAccessionNo = "**********";
		tagInfo.strStudyDate = "20000607";
		//m_pStore->SendFileStoreScu(u8"D:/work/dicom/test_data/AC/AC_DICOM.dcm");
		m_pStore->ConnectNet();
		//	这个是可以正常发送到ge 工作站的
		m_pStore->SendFileStoreScuGe(u8"D:/PicEx/124.jpg", true, tagInfo);
		m_pStore->SendFileStoreScuGe(u8"D:/PicEx/1235.jpg", true, tagInfo);
		//m_pStore->SendFileStoreScu(u8"D://work//MyWork//GeViewPoint_file//test_demo//US570256//00196249.dcm", tagInfo);
		//m_pStore->SendFileStoreScu(u8"D:/Pic/Test/00930441", tagInfo);
		//m_pStore->SendFileStoreScu(u8"D:/Pic/New.dcm");

		/*tagPatientShotPicInfo tagInfo;
		tagInfo.llCheckTimeStamp = time(nullptr);
		tagInfo.nPatientSex = 2;
		tagInfo.nPatientAge = 29;


		//tagInfo.strStudyUID = "1.2.392.200046.100.2.1.************.161128190137";

		tagInfo.strPicDescription = "经丘脑横切面";
		tagInfo.strPicId = "**********";
		tagInfo.strStudyDate = "03";
		tagInfo.strPatLocalId = "20250707001";
		tagInfo.strPatientName = "Mike^text";
		tagInfo.strAccessionNo = "**********";
		tagInfo.strStudyDate = "20250311";
		LPM::CreateDcmPicEx("D:/PicEx/24.jpg", 1, tagInfo, "D:/work/MyWork/GeViewPoint_file/CreateDicomFile/test1.dcm");*/
		
	
	}
}


void CDicomOptLibDemoDlg::OnBnClickedBtnstoredis()
{
	if (m_pStore)
	{
		m_pStore->DisconnectNet();
	}
}


void CDicomOptLibDemoDlg::OnBnClickedBtndicomtojpg()
{
	bool bRet = LPM::ReadDcmPic("D://work//MyWork//GeViewPoint_file//dicom_has_acc//**********//202522001_1.dcm", "D://work//MyWork//GeViewPoint_file//dicom_has_acc//**********//202522001_1");
	int i = 90;
}


void CDicomOptLibDemoDlg::OnBnClickedBtnworklistquery()
{
	m_pFind->WorklistScu();
}


void CDicomOptLibDemoDlg::OnBnClickedBtnreaddcminfo()
{
	LPM::ReadDcmInfo("D:\\work\\MyWork\\GeViewPoint_file\\SR841224\\00847588");
	std::string strTemp = "1234~890";
	int nIndex = strTemp.find("~");
	if (nIndex > 0)
	{
		strTemp = strTemp.substr(nIndex+1);
	}
}


void CDicomOptLibDemoDlg::OnBnClickedButton1()
{
	// TODO: 在此添加控件通知处理程序代码
}

static int StoreScpCallback(const StoreScpPicInfo& _objStoreInfo, void* _pUserData)
{
	int nRet = -1;
	LPM::WriteLogEx(LPM::EmModuleType::ModuleLogic,LPM:: LogDebug, u8"image count%d, path:%s, AccessionNumber:%s", _objStoreInfo.nCaseImageCount, _objStoreInfo.strSavePathUtf8.c_str(), _objStoreInfo.strAccessionNumber.c_str());
	return nRet;
}
void CDicomOptLibDemoDlg::OnBnClickedBtndicomscpbegin()
{
	if (!m_pStoreScp)
	{
		m_pStoreScp = new LPM::CDcmStoreScpEx();
		
	}
	if (m_pStoreScp)
	{
		m_pStoreScp->InitParam(StoreScpCallback, this, 9090, "ayj_ai_qc_center", "D://var/dicomStoreEx");
		m_pStoreScp->StartServer();
	}
}

void CDicomOptLibDemoDlg::OnBnClickedBtndicomscpstop()
{
	if (m_pStoreScp)
	{
		m_pStoreScp->StopServer();
	}
}


void CDicomOptLibDemoDlg::OnBnClickedBtndicomechoscu()
{
	
	//m_pStore->InitParam("*************", 110, "VP", "SUPAICS");
	if (LPM::CDcmCGetScu::SendDicomEcho("*************", 110, "VP", "SUPAICS",3))
	{
		LPM::WriteLogEx(LPM::EmModuleType::ModuleLogic, LPM::LogDebug, u8"Echo 成功");
	}
	else
	{
		LPM::WriteLogEx(LPM::EmModuleType::ModuleLogic, LPM::LogDebug, u8"Echo 失败");
	}
	
}

struct MyComparator
{
	bool operator()(const CString& lhs, const CString& rhs) const
	{
		// 降序排序
		return lhs < rhs;
	}
};

#include <map>
void CDicomOptLibDemoDlg::OnBnClickedOk()
{
	
	// 使用自定义比较函数的map
	std::map<CString , std::string> reversedOrderedMap;

	// 插入键值对
	reversedOrderedMap[L"张三"] = "one";
	reversedOrderedMap[L"填写"] = "two";
	reversedOrderedMap[L"玫瑰"] = "four";
	reversedOrderedMap[L"历史"] = "five";
	reversedOrderedMap[L"王五"] = "three";
	
	// TODO: 在此添加控件通知处理程序代码
	CDialogEx::OnOK();
}





void CDicomOptLibDemoDlg::OnBnClickedBtncreatemerdcmfile()
{
	//if (m_pStore)
	{
		tagStoreMeasuredInfo obj;
		obj.strPatientId = "250311001";
		obj.strPatientName = "test^1";
		obj.strAccessionNumber = "03110001";
		obj.strStudyDate = "20250311";
		m_pStore->CreateMeasuredDcmFile("D:/work/MyWork/GeViewPoint_file/CreateDicomFile/testMer", obj);
	}
}


void CDicomOptLibDemoDlg::OnBnClickedBtnmodifymerdcmfile()
{
	// TODO: 在此添加控件通知处理程序代码
	tagStoreMeasuredInfo obj;
	obj.strPatientId = "250311001";
	obj.strPatientName = "test^1";
	obj.strAccessionNumber = "03110001";
	obj.strStudyDate = "20250311";
	m_pStore->ModifyMeasurementDcmFile("D://work//MyWork//GeViewPoint_file//demo_data_2//SR982386//00868303", "D://work//MyWork//GeViewPoint_file//bpd.dcm", obj);
}


void CDicomOptLibDemoDlg::OnBnClickedBtncreatedicompic()
{
	tagPatientShotPicInfo tagInfo;
	tagInfo.llCheckTimeStamp = time(nullptr);
	tagInfo.nPatientSex = 2;
	tagInfo.nPatientAge = 29;
	
	
	//tagInfo.strStudyUID = "1.2.392.200046.100.2.1.************.161128190137";
	
	tagInfo.strPicDescription = "经丘脑横切面";
	tagInfo.strPicId = "**********";
	tagInfo.strStudyDate = "03";
	tagInfo.strPatLocalId = "20250707001";
	tagInfo.strPatientName = "Mike^text";
	tagInfo.strAccessionNo = "**********";
	tagInfo.strStudyDate = "20250311";
	LPM::CreateDcmPicEx("D:/PicEx/24.jpg", 1, tagInfo, "D:/work/MyWork/GeViewPoint_file/CreateDicomFile/test1.dcm");
}


void CDicomOptLibDemoDlg::OnBnClickedBtnmodifypicinfo()
{
	tagStoreMeasuredInfo obj;
	obj.strPatientId = "250308001";
	obj.strPatientName = "2025^0308";
	obj.strAccessionNumber = "2503002";
	obj.strStudyDate = "20250317";
	m_pStore->ModifyMeasurementDcmFile("D://work//MyWork//GeViewPoint_file//test_demo//US570256//00196249", "D://work//MyWork//GeViewPoint_file//test_demo//US570256//00196249.dcm", obj);
}
