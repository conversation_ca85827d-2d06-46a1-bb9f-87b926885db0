#include "pch.h"
#include "DcmStoreScu.h"

#include "DcmHead.h"
//	如遇到压缩型JPEG保存的DICOM图片， 需使用DJDecoderRegistration::registerCodecs(); 
//	注册解压算法，然后再读取DICOM图片就能读出来了。用完后用DJDecoderRegistration::cleanup(); 释放内存。注意用这个的时候需要
#include <iostream>

using namespace std;

namespace LPM
{
	static int g_nOptAcseTimeout = 30;//	超时设置 s
	static int g_nOptDimseTimeout = 0;

	CLock g_objApiLock;

	CDcmStoreScu::CDcmStoreScu()
	{
		OFStandard::initializeNetwork();
		DJDecoderRegistration::registerCodecs();
	}

	CDcmStoreScu::~CDcmStoreScu()
	{
		DJDecoderRegistration::cleanup();
		//	关闭网络
		OFStandard::shutdownNetwork();
	}

	int CDcmStoreScu::InitParam(const char* _szRemoteIp, const int _nRemotePort, const char* _szRemoteTitle, const char* _szLocalTitle)
	{
		if (_szRemoteIp == nullptr || _nRemotePort <=0 || _szRemoteTitle == nullptr || _szLocalTitle == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, u8"CDicomOpt::InitNet , 参数不合法 !");
			return -1;
		}

		if (m_pStoreScuParams)
		{
			WriteLogEx(ModuleVideo, LogError, u8"CDicomOpt::InitNet , 已初始化 !");
			return -1;
		}

		CAutoLock tempLock(&g_objApiLock);
		memset(m_szRemoteIp, 0, 256);
		memset(m_szRemoteTitle, 0, 256);
		memset(m_szLocalTitle, 0, 256);

		memcpy(m_szRemoteIp, _szRemoteIp, strlen(_szRemoteIp));

		m_nRemotePort = _nRemotePort;
		
		memcpy(m_szRemoteTitle, _szRemoteTitle, strlen(_szRemoteTitle));
		memcpy(m_szLocalTitle, _szLocalTitle, strlen(_szLocalTitle));


		return 0;
	}

	//	连接网络
	int CDcmStoreScu::ConnectNet()
	{
		CAutoLock tempLock(&g_objApiLock);

		std::string strRemoteIp(m_szRemoteIp);
		if (m_nRemotePort <= 0 || strRemoteIp.empty())
			return -1;

		CraateParam();
		CreateNet();
		int nRet = CreateAssoc();

		return nRet;
	}

	int CDcmStoreScu::CraateParam()
	{
		if (m_pStoreScuParams)
			return -1;

		//	2	初始化关联参数，即创建一个T_ASC_Parameters* 的实例
		OFCondition cond = ASC_createAssociationParameters(&m_pStoreScuParams, ASC_DEFAULTMAXPDU);
		if (cond.bad())
		{
			OFString strTemp;
			WriteLogEx(ModuleVideo, LogError, u8"创建一个T_ASC_Parameters* 的实例 失败，%s ", DimseCondition::dump(strTemp, cond).c_str());
			return -1;
		}

		//	3	设置客户端和服务端的 AETitle
		//ASC_setAPTitles(m_pStoreScuParams, "AyjPaics", m_szRemoteTitle, nullptr);
		ASC_setAPTitles(m_pStoreScuParams, m_szLocalTitle, m_szRemoteTitle, nullptr);
		cond = ASC_setTransportLayerType(m_pStoreScuParams, false);
		if (cond.bad())
		{
			WriteLogEx(ModuleVideo, LogError, u8"设置传送层安全类型失败:%s", cond.text());
			return -1;
		}
		//	4	设置服务端的地址和端口
		DIC_NODENAME tagLocalHost = {};
		gethostname(tagLocalHost, sizeof(tagLocalHost) - 1);
		DIC_NODENAME tagRemoteHost = {};
		sprintf(tagRemoteHost, "%s:%d", m_szRemoteIp, OFstatic_cast(int, m_nRemotePort));

		ASC_setPresentationAddresses(m_pStoreScuParams, tagLocalHost, tagRemoteHost);

		//	5	根据要传输的 dicom 对象的 sop class uid ，设置传输语法和抽象语法
		int nPresentId = 1;
		//const char cAbstractSyntax[]{ UID_SecondaryCaptureImageStorage };//屏幕拷贝 未压缩的图片
		const char cAbstractSyntax[]{ UID_UltrasoundImageStorage };// 压缩过的图片
		const char* szTransferSyntaxList[]{ UID_LittleEndianImplicitTransferSyntax };//小端隐式传输语法
		cond = ASC_addPresentationContext(m_pStoreScuParams, nPresentId, cAbstractSyntax, szTransferSyntaxList, 1);
		if (cond.bad())
		{
			cout << cond.text() << endl;
			WriteLogEx(ModuleVideo, LogError, u8"ASC_addPresentationContext  %s", cond.text());
			return -1;
		}
		 nPresentId += 2;
		//const char cAbstractSyntax[]{ UID_SecondaryCaptureImageStorage };//屏幕拷贝 未压缩的图片
		const char cAbstractSyntaxSR[]{ UID_ComprehensiveSRStorage };// 压缩过的图片
		const char* szTransferSyntaxListSR[]{ UID_LittleEndianImplicitTransferSyntax  };//小端隐式传输语法
		cond = ASC_addPresentationContext(m_pStoreScuParams, nPresentId, cAbstractSyntaxSR, szTransferSyntaxListSR, 1);
		if (cond.bad())
		{
			cout << cond.text() << endl;
			WriteLogEx(ModuleVideo, LogError, u8"ASC_addPresentationContext  %s", cond.text());
			return -1;
		}

		return 0;
	}

	int CDcmStoreScu::CreateNet()
	{
		if (m_pStoreScuNet)
			return -1;
		//	1	初始化网络，即创建一个T_ASC_Network*的实例
		OFCondition cond = ASC_initializeNetwork(NET_REQUESTOR, 0, g_nOptAcseTimeout, &m_pStoreScuNet);
		if (cond.bad())
		{
			//WriteLogEx(ModuleVideo, LogError, "DICOM 底层网络初始化失败");
			return -1;
		}

		return 0;
	}
	
	int CDcmStoreScu::CreateAssoc()
	{
		if (m_pAssoc)
			return -1;

		if (!m_pStoreScuNet || !m_pStoreScuParams)
			return -1;

		//	创建连接
		OFCondition cond = ASC_requestAssociation(m_pStoreScuNet, m_pStoreScuParams, &m_pAssoc);
		if (cond.bad())
		{
			WriteLogEx(ModuleVideo, LogError, u8"ASC_requestAssociation 连接服务器失败, %s", LPM::W2U(LPM::A2WEx( cond.text())).c_str());
			return -1;
		}

		if (ASC_countAcceptedPresentationContexts(m_pStoreScuParams) == 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"服务端不接受制定的抽象语法与传输语法");
			return -1;
		}

		m_bConnectScp = true;

		return 0;
	}

	//	断开网络
	int CDcmStoreScu::DisconnectNet()
	{
		//	解除关联，即终止到SCP的网络连接
		CAutoLock tempLock(&g_objApiLock);
		OFCondition cond = {};
		if (m_pAssoc)
		{
			cond = ASC_releaseAssociation(m_pAssoc);	//	释放关联
			cond = ASC_destroyAssociation(&m_pAssoc);				//	销毁
		}
		if (m_pStoreScuNet)
		{
			cond = ASC_dropNetwork(&m_pStoreScuNet);
		}

		m_pStoreScuParams = nullptr;

		m_bConnectScp = false;
		
		return 0;
	}

	int CDcmStoreScu::SendFileStoreScu(const char* _szDcmFilePath, const tagPatientShotPicInfo& _objInfo, bool _bIsGeServer)
	{
		//	1	加载 dicom 文件
		DcmFileFormat objDcmFile;
		OFCondition cond = objDcmFile.loadFile(_szDcmFilePath, EXS_Unknown, EGL_noChange, DCM_MaxReadLength, ERM_autoDetect);
		if (cond.bad())		
			return -1;		

		//	2、添加患者信息
		if(_bIsGeServer)
			AddPicInfoGe(objDcmFile.getDataset(), _objInfo);
		else
			AddPicInfo(objDcmFile.getDataset(), _objInfo);

		//	3、保存测试
		/*string strPath1;
		if (strPath1.empty())
		{
			wstring wstrPath = GetCurExePath();
			strPath1 = LPM::W2AEx(wstrPath) + "tempFile1.dcm";
			objDcmFile.saveFile(strPath1.c_str());
		}*/

		//	4、发送到服务器
		return  SendFileStoreScu(&objDcmFile);
	}
	
	int CDcmStoreScu::SendFileStoreScu(DcmFileFormat* _objFileFmt)
	{
		if (nullptr == m_pAssoc)
			return -1;
		
		//	2	找出哪些SOP类和SOP实例被封装在文件中
		OFCondition cond = {};
		T_ASC_PresentationContextID presID = {};
		DIC_UI sopClass = {};
		DIC_UI sopInstance = {};

		if (!DU_findSOPClassAndInstanceInDataSet(_objFileFmt->getDataset(),
			sopClass, sizeof(sopClass), sopInstance, sizeof(sopInstance), OFFalse))
		{
			WriteLogEx(ModuleVideo, LogError, u8"文件中没有SOP类或实例UID:%s");
			return -1;
		}
		
		//	找出应该使用哪些可接受的表示上下文
		DcmXfer filexfer(_objFileFmt->getDataset()->getOriginalXfer());
		if (filexfer.isNotEncapsulated())
			filexfer = EXS_DeflatedLittleEndianExplicit;
		else
		{
			//传输语法解压或转换
			filexfer = UID_LittleEndianImplicitTransferSyntax;
			cond = _objFileFmt->getDataset()->chooseRepresentation(filexfer.getXfer(), NULL);
			if (cond.bad())
				return -1;
		}

		if (filexfer.getXfer() != EXS_Unknown)
			presID = ASC_findAcceptedPresentationContextID(m_pAssoc, sopClass, filexfer.getXferID());
		else
			presID = ASC_findAcceptedPresentationContextID(m_pAssoc, sopClass);

		if (presID == 0)
		{
			const char *modalityName = dcmSOPClassUIDToModality(sopClass);
			if (!modalityName)
				modalityName = dcmFindNameOfUID(sopClass);
			if (!modalityName)
				modalityName = "unknown SOP class";
			cout << "No valid presentation context ID , for: (" << modalityName << ") " << sopClass;

			WriteLogEx(ModuleVideo, LogError, u8"No valid presentation context ID , for: (%s) %s", modalityName, sopClass);
			return -1;
		}
		//return -1;
		/*string strPath;
		if (strPath.empty() && _objFileFmt)
		{
			wstring wstrPath = GetCurExePath();
			strPath = LPM::W2AEx(wstrPath) + "tempFile.dcm";
			_objFileFmt->saveFile(strPath.c_str(), EXS_LittleEndianExplicit);
		}*/
		
		T_ASC_PresentationContext pc;
		ASC_findAcceptedPresentationContext(m_pAssoc->params, presID, &pc);
		DcmXfer netTransfer(pc.acceptedTransferSyntax);

		//	如果需要，转储有关传输语法的一般信息

		//	准备数据传输
		T_DIMSE_C_StoreRQ objReq = {};
		T_DIMSE_C_StoreRSP objRsp = {};
		memset(OFreinterpret_cast(char *, &objReq), 0, sizeof(objReq));
		DIC_US msgId = m_pAssoc->nextMsgID++;
		objReq.MessageID = msgId;

		OFStandard::strlcpy(objReq.AffectedSOPClassUID, sopClass, sizeof(objReq.AffectedSOPClassUID));
		OFStandard::strlcpy(objReq.AffectedSOPInstanceUID, sopInstance, sizeof(objReq.AffectedSOPInstanceUID));
		objReq.DataSetType = DIMSE_DATASET_PRESENT;
		objReq.Priority = DIMSE_PRIORITY_MEDIUM;

		int nRet = -1;
		//	进行数据传输
		DcmDataset* pStatusDetail = nullptr;
		cond = DIMSE_storeUser(m_pAssoc, presID, &objReq, nullptr, _objFileFmt->getDataset(), StoreScuCallback, nullptr,
			(T_DIMSE_BlockingMode)m_nBlockingMode, g_nOptDimseTimeout, &objRsp, &pStatusDetail, nullptr);

		if (cond == EC_Normal && (objRsp.DimseStatus == STATUS_Success || DICOM_WARNING_STATUS(objRsp.DimseStatus)))
		{
			WriteLogEx(ModuleVideo, LogDebug, u8"DIMSE_storeUser success");
			nRet = 0;
		}
		else
		{
			WriteLogEx(ModuleVideo, LogError, u8"DIMSE_storeUser error :%s", LPM::W2U(LPM::A2WEx(cond.text()).c_str()).c_str());
			nRet = -1;
		}
		int nLastStatusCode = objRsp.DimseStatus;
		if (pStatusDetail)
		{
			delete pStatusDetail;
			pStatusDetail = nullptr;
		}

		return nRet;
	}
	
	int CDcmStoreScu::SendFileStoreScu(const char* _szPicFilePath, bool _bJpgOrBmp, const tagPatientShotPicInfo& _objInfo)
	{
		if (_szPicFilePath == nullptr)
			return -1;
		static string strPath;
		if (strPath.empty())
		{
			wstring wstrPath = GetCurExePath();
			strPath = LPM::W2AEx(wstrPath) + "tempFile.dcm";
		}
		

		CAutoLock tempLock(&g_objApiLock);

		bool bOk = CreateDcmPicEx(LPM::U2A(_szPicFilePath).c_str(), true, _objInfo, strPath.c_str());

		WriteLogEx(ModuleVideo, LogDebug, u8"Dicom Store file: %s ", _szPicFilePath);

		if (bOk)
			return SendFileStoreScu(strPath.c_str(), _objInfo);
		else
			WriteLogEx(ModuleVideo, LogError, u8"Bad DICOM file: %s", _szPicFilePath);

		return -1;
	}

	int CDcmStoreScu::SendFileStoreScuGe(const char* _szPicFilePath, bool _bJpgOrBmp, const tagPatientShotPicInfo& _objInfo)
	{
		if (_szPicFilePath == nullptr)
			return -1;
		
		int nRet = -1;

		CAutoLock tempLock(&g_objApiLock);

		//	1、创建dicom 图片数据
		DcmDataset* pResultObject = nullptr;
		E_TransferSyntax eWriteXfer = EXS_Unknown;
		bool bOk = CreateDcmPicEx(pResultObject, eWriteXfer, LPM::U2A(_szPicFilePath).c_str(), true, _objInfo);
		
		//	2、发送给scp
		if (bOk && pResultObject)
		{
			AddPicInfoGe(pResultObject, _objInfo);
			DcmFileFormat objFormat(pResultObject);
			objFormat.saveFile("D://test.dcm", eWriteXfer);
			nRet = SendFileStoreScu(&objFormat);
			WriteLogEx(ModuleVideo, LogDebug, u8"Dicom iamge store file %s: %s ", nRet ==0 ? "Ok":"error", _szPicFilePath);
		}
		else
			WriteLogEx(ModuleVideo, LogError, u8"Dicom iamge store file error: %s", _szPicFilePath);

		//	3、清除缓存
		delete pResultObject;
		pResultObject = nullptr;

		return nRet;
	}

	//	发送测量值 到工作站
	int CDcmStoreScu::SendFileStoreScuMeasuredGe(const tagStoreMeasuredInfo& _objInfo, const char* _szSaveDicomFilePath )
	{
		//	1	加载 dicom 文件
		DcmFileFormat objDcmFile;
		
		//	2、创建测量值数据
		CreateMeasuredDcmFileEx(&objDcmFile, _objInfo, _szSaveDicomFilePath);
		

		//	2、发送到服务器
		return  SendFileStoreScu(&objDcmFile);
	}
	
	// CreateMeasuredDcmFileEx 函数实现完成
	// 该函数创建一个符合DICOM SR标准的超声测量值文件
	// 包含观察者信息、胎儿摘要和基本测量数据
	//
	// 主要特点：
	// 1. 符合DICOM SR (Structured Report) 标准
	// 2. 包含完整的元数据信息
	// 3. 支持超声测量值的结构化存储
	// 4. 兼容DCMTK 3.6.6版本
	// 5. 生成的文件可被标准DICOM查看器识别
	int CDcmStoreScu::CreateMeasuredDcmFileEx(DcmFileFormat* pObjDcmFile, const tagStoreMeasuredInfo& _objInfo, const char* _szSavecmFilePathUtf8)
	{
		if (_objInfo.vectMeasured.size() <= 0  )
		{
			WriteLogEx(ModuleVideo, LogError, u8"not input measured info");
			return -1;
		}

		try {
			// 1. 创建DICOM文件格式对象
			DcmDataset* pDataset = nullptr;
			DcmFileFormat ObjDcmFile;
			if(pObjDcmFile == nullptr)
				pDataset = ObjDcmFile.getDataset();
			else
				pDataset = pObjDcmFile->getDataset();

			if (!pDataset)
			{
				WriteLogEx(ModuleVideo, LogError, u8"can not create dicom data");
				return -1;
			}

			// 2. 设置基本DICOM头信息
			char szUID[100] = {};
			dcmGenerateUniqueIdentifier(szUID, SITE_INSTANCE_UID_ROOT);
			pDataset->putAndInsertString(DCM_SOPClassUID, UID_ComprehensiveSRStorage);
			pDataset->putAndInsertString(DCM_SOPInstanceUID, szUID);
			pDataset->putAndInsertString(DCM_SpecificCharacterSet, "ISO_IR 100");

			// 设置日期时间
			OFString strTime;
			DcmTime::getCurrentTime(strTime);
			pDataset->putAndInsertString(DCM_InstanceCreationDate, _objInfo.strStudyDate.c_str());
			pDataset->putAndInsertString(DCM_StudyDate, _objInfo.strStudyDate.c_str());
			pDataset->putAndInsertString(DCM_ContentDate, _objInfo.strStudyDate.c_str());
			pDataset->putAndInsertString(DCM_InstanceCreationTime, strTime.c_str());
			pDataset->putAndInsertString(DCM_StudyTime, strTime.c_str());
			pDataset->putAndInsertString(DCM_ContentTime, strTime.c_str());

			// 设置模态和制造商信息
			pDataset->putAndInsertString(DCM_Modality, "SR");
			pDataset->putAndInsertString(DCM_Manufacturer, "AiriScan");
			pDataset->putAndInsertString(DCM_ManufacturerModelName, "AiriScan AI");
			pDataset->putAndInsertString(DCM_SoftwareVersions, "2.0.0.250707");

			// 3. 设置患者和研究信息
			pDataset->putAndInsertString(DCM_PatientName, _objInfo.strPatientName.c_str());
			pDataset->putAndInsertString(DCM_PatientID, _objInfo.strPatientId.c_str());
			pDataset->putAndInsertString(DCM_PatientSex, "F");
			pDataset->putAndInsertString(DCM_AccessionNumber, _objInfo.strAccessionNumber.c_str());

			char szStudyUID[100] = {}, szSeriesUID[100] = {};
			dcmGenerateUniqueIdentifier(szStudyUID, SITE_STUDY_UID_ROOT);
			dcmGenerateUniqueIdentifier(szSeriesUID, SITE_SERIES_UID_ROOT);
			pDataset->putAndInsertString(DCM_StudyInstanceUID, szStudyUID);
			pDataset->putAndInsertString(DCM_SeriesInstanceUID, szSeriesUID);
			pDataset->putAndInsertString(DCM_StudyID, "1");
			pDataset->putAndInsertString(DCM_SeriesNumber, "1");
			pDataset->putAndInsertString(DCM_InstanceNumber, "1");

			// 4. 设置SR特有属性
			pDataset->putAndInsertString(DCM_ValueType, "CONTAINER");
			pDataset->putAndInsertString(DCM_ContinuityOfContent, "SEPARATE");
			pDataset->putAndInsertString(DCM_CompletionFlag, "PARTIAL");
			pDataset->putAndInsertString(DCM_VerificationFlag, "UNVERIFIED");

			// 5. 创建根文档概念名称
			DcmSequenceOfItems* pConceptNameSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
			DcmItem* pConceptItem = new DcmItem();
			pConceptItem->putAndInsertString(DCM_CodeValue, "125000");
			pConceptItem->putAndInsertString(DCM_CodingSchemeDesignator, "DCM");
			pConceptItem->putAndInsertString(DCM_CodeMeaning, "OB-GYN Ultrasound Procedure Report");
			pConceptNameSeq->insert(pConceptItem);
			pDataset->insert(pConceptNameSeq);

			// 6. 创建主要内容序列 - 包含测量数据
			DcmSequenceOfItems* pMainContentSeq = new DcmSequenceOfItems(DCM_ContentSequence);

			// 创建生物测量容器
			DcmItem* pBiometryContainer = CreateBiometryContainer(_objInfo.vectMeasured);
			if (pBiometryContainer)
			{
				pMainContentSeq->insert(pBiometryContainer);
				WriteLogEx(ModuleVideo, LogInfo, "add %d Measurements", _objInfo.vectMeasured.size());
			}

			// 将主内容序列插入数据集
			pDataset->insert(pMainContentSeq);
			if (_szSavecmFilePathUtf8 != nullptr)
			{
				// 7. 保存文件
				OFCondition status;
				if(pObjDcmFile)
					status = pObjDcmFile->saveFile(LPM::U2AEx(_szSavecmFilePathUtf8).c_str(), EXS_LittleEndianExplicit);
				else
					status = ObjDcmFile.saveFile(LPM::U2AEx(_szSavecmFilePathUtf8).c_str(), EXS_LittleEndianExplicit);

				if (status.bad())
					WriteLogEx(ModuleVideo, LogError, "save dicom measurements file error: %s", LPM::W2U(LPM::A2WEx(status.text()).c_str()).c_str());
				else
					WriteLogEx(ModuleVideo, LogDebug, "save dicom measurements file ok: %s", _szSavecmFilePathUtf8);
			}

			return 0;
			
		}
		catch (const std::exception& e) {
			WriteLogEx(ModuleVideo, LogError, "create dicom measurements file error: %s", e.what());
			return -1;
		}

		return -1;
	}

	// CreateMeasuredDcmFileExWithCustomData - 支持自定义测量值
	/*int CDcmStoreScu::CreateMeasuredDcmFileExWithCustomData(const char* _szSavecmFilePathUtf8, const std::vector<MeasurementData>& customMeasurements)
	{
		if (_szSavecmFilePathUtf8 == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, u8"bad save path");
			return -1;
		}

		if (customMeasurements.empty())
		{
			WriteLogEx(ModuleVideo, LogWarning, "comm Measured is empty, user default measured");
			//return CreateMeasuredDcmFileEx(_szSavecmFilePathUtf8);
		}

		int nRet = -1;

		try {
			// 1. 创建DICOM文件格式对象
			DcmFileFormat fileformat;
			DcmDataset* pDataset = fileformat.getDataset();
			if (!pDataset) {
				WriteLogEx(ModuleVideo, LogError, u8"can not create dicom data");
				return -1;
			}

			// 2. 设置基本DICOM头信息
			char szUID[100] = {};
			dcmGenerateUniqueIdentifier(szUID, SITE_INSTANCE_UID_ROOT);

			// 设置SOP类和实例UID
			pDataset->putAndInsertString(DCM_SOPClassUID, UID_ComprehensiveSRStorage);
			pDataset->putAndInsertString(DCM_SOPInstanceUID, szUID);

			// 设置字符集
			pDataset->putAndInsertString(DCM_SpecificCharacterSet, "ISO_IR 100");

			// 设置创建日期时间
			OFString strDate, strTime;
			DcmDate::getCurrentDate(strDate);
			DcmTime::getCurrentTime(strTime);
			pDataset->putAndInsertString(DCM_InstanceCreationDate, strDate.c_str());
			pDataset->putAndInsertString(DCM_StudyDate, strDate.c_str());
			pDataset->putAndInsertString(DCM_ContentDate, strDate.c_str());
			pDataset->putAndInsertString(DCM_InstanceCreationTime, strTime.c_str());
			pDataset->putAndInsertString(DCM_StudyTime, strTime.c_str());
			pDataset->putAndInsertString(DCM_ContentTime, strTime.c_str());

			// 设置实例创建者UID
			pDataset->putAndInsertString(DCM_InstanceCreatorUID, "1.2.276.0.7230010.3.0.3.6.0");

			// 设置访问号
			pDataset->putAndInsertString(DCM_AccessionNumber, "25111001");

			// 设置模态
			pDataset->putAndInsertString(DCM_Modality, "SR");

			// 设置制造商信息
			pDataset->putAndInsertString(DCM_Manufacturer, "GE Healthcare Austria GmbH & Co OG");
			pDataset->putAndInsertString(DCM_ManufacturerModelName, "4DView");
			pDataset->putAndInsertString(DCM_DeviceSerialNumber, "4DView");
			pDataset->putAndInsertString(DCM_SoftwareVersions, "18 Ext.3 Rev.b");

			// 设置研究描述
			pDataset->putAndInsertString(DCM_StudyDescription, "Ultrasound Biometry Report");

			// 3. 设置患者信息
			pDataset->putAndInsertString(DCM_PatientName, "2025^0308");
			pDataset->putAndInsertString(DCM_PatientID, "250308001");
			pDataset->putAndInsertString(DCM_PatientSex, "F");

			// 4. 设置研究和系列信息
			char szStudyUID[100] = {}, szSeriesUID[100] = {};
			dcmGenerateUniqueIdentifier(szStudyUID, SITE_STUDY_UID_ROOT);
			dcmGenerateUniqueIdentifier(szSeriesUID, SITE_SERIES_UID_ROOT);

			pDataset->putAndInsertString(DCM_StudyInstanceUID, szStudyUID);
			pDataset->putAndInsertString(DCM_SeriesInstanceUID, szSeriesUID);
			pDataset->putAndInsertString(DCM_StudyID, "1");
			pDataset->putAndInsertString(DCM_SeriesNumber, "1");
			pDataset->putAndInsertString(DCM_InstanceNumber, "1");

			// 5. 设置SR特有属性
			pDataset->putAndInsertString(DCM_ValueType, "CONTAINER");
			pDataset->putAndInsertString(DCM_ContinuityOfContent, "SEPARATE");
			pDataset->putAndInsertString(DCM_CompletionFlag, "PARTIAL");
			pDataset->putAndInsertString(DCM_VerificationFlag, "UNVERIFIED");

			// 6. 创建概念名称代码序列 - 根文档类型
			DcmSequenceOfItems* pConceptNameSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
			DcmItem* pConceptItem = new DcmItem();
			pConceptItem->putAndInsertString(DCM_CodeValue, "125000");
			pConceptItem->putAndInsertString(DCM_CodingSchemeDesignator, "DCM");
			pConceptItem->putAndInsertString(DCM_CodeMeaning, "OB-GYN Ultrasound Procedure Report");
			pConceptNameSeq->insert(pConceptItem);
			pDataset->insert(pConceptNameSeq);

			// 7. 创建空的执行过程代码序列
			DcmSequenceOfItems* pProcSeq = new DcmSequenceOfItems(DCM_PerformedProcedureCodeSequence);
			pDataset->insert(pProcSeq);

			// 8. 创建内容模板序列
			DcmSequenceOfItems* pTemplateSeq = new DcmSequenceOfItems(DCM_ContentTemplateSequence);
			DcmItem* pTemplateItem = new DcmItem();
			pTemplateItem->putAndInsertString(DCM_MappingResource, "DCMR");
			pTemplateItem->putAndInsertString(DCM_TemplateIdentifier, "5000");
			pTemplateSeq->insert(pTemplateItem);
			pDataset->insert(pTemplateSeq);

			// 9. 创建主要内容序列
			DcmSequenceOfItems* pMainContentSeq = new DcmSequenceOfItems(DCM_ContentSequence);

			// 9.1 添加观察者信息容器
			DcmItem* pObserverItem = new DcmItem();
			pObserverItem->putAndInsertString(DCM_RelationshipType, "CONTAINS");
			pObserverItem->putAndInsertString(DCM_ValueType, "CONTAINER");
			pObserverItem->putAndInsertString(DCM_ContinuityOfContent, "SEPARATE");

			DcmSequenceOfItems* pObsConceptSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
			DcmItem* pObsConceptItem = new DcmItem();
			pObsConceptItem->putAndInsertString(DCM_CodeValue, "121005");
			pObsConceptItem->putAndInsertString(DCM_CodingSchemeDesignator, "DCM");
			pObsConceptItem->putAndInsertString(DCM_CodeMeaning, "Observer Type");
			pObsConceptSeq->insert(pObsConceptItem);
			pObserverItem->insert(pObsConceptSeq);

			// 添加观察者内容
			DcmSequenceOfItems* pObsContentSeq = new DcmSequenceOfItems(DCM_ContentSequence);

			// 观察者姓名
			DcmItem* pNameItem = new DcmItem();
			pNameItem->putAndInsertString(DCM_RelationshipType, "HAS OBS CONTEXT");
			pNameItem->putAndInsertString(DCM_ValueType, "PNAME");

			DcmSequenceOfItems* pNameConceptSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
			DcmItem* pNameConceptItem = new DcmItem();
			pNameConceptItem->putAndInsertString(DCM_CodeValue, "121008");
			pNameConceptItem->putAndInsertString(DCM_CodingSchemeDesignator, "DCM");
			pNameConceptItem->putAndInsertString(DCM_CodeMeaning, "Person Observer Name");
			pNameConceptSeq->insert(pNameConceptItem);
			pNameItem->insert(pNameConceptSeq);

			pNameItem->putAndInsertString(DCM_PersonName, "Billy Hou");
			pObsContentSeq->insert(pNameItem);

			pObserverItem->insert(pObsContentSeq);
			pMainContentSeq->insert(pObserverItem);

			// 9.2 添加自定义生物测量容器
			WriteLogEx(ModuleVideo, LogInfo, u8"添加自定义胎儿生物测量数据...");

			DcmItem* pBiometryContainer = this->CreateBiometryContainer(customMeasurements);
			if (pBiometryContainer) {
				pMainContentSeq->insert(pBiometryContainer);
				WriteLogEx(ModuleVideo, LogInfo, u8"Success add  %d Measurements info ", customMeasurements.size());
			}

			// 将主内容序列插入数据集
			pDataset->insert(pMainContentSeq);

			// 10. 保存文件
			OFCondition status = fileformat.saveFile(LPM::U2AEx(_szSavecmFilePathUtf8).c_str(), EXS_LittleEndianExplicit);
			if (status.bad()) {
				WriteLogEx(ModuleVideo, LogError, u8"保存DICOM文件失败: %s",
					LPM::W2U(LPM::A2WEx(status.text()).c_str()).c_str());
				nRet = -1;
			} else {
				WriteLogEx(ModuleVideo, LogDebug, u8"DICOM自定义测量值文件创建成功: %s", _szSavecmFilePathUtf8);
				nRet = 0;
			}
		}
		catch (const std::exception& e) {
			WriteLogEx(ModuleVideo, LogError, u8"创建DICOM文件异常: %s", e.what());
			nRet = -1;
		}

		return nRet;
	}
	*/
	int CDcmStoreScu::PrintMeasuredInfoToLogFile(const char* _szDcmFilePathUtf8)
	{
		if (_szDcmFilePathUtf8 == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, u8"input dcm file path error");
			return -1;
		}

		int nRet = -1;

		try {
			// 1. 加载DICOM文件
			DcmFileFormat fileformat;
			OFCondition status = fileformat.loadFile(LPM::U2AEx(_szDcmFilePathUtf8).c_str());
			if (status.bad())
			{
				WriteLogEx(ModuleVideo, LogError, u8"无法打开DICOM文件: %s", _szDcmFilePathUtf8);
				return -1;
			}

			// 2. 获取数据集
			DcmDataset* pDataset = fileformat.getDataset();
			if (!pDataset)
			{
				WriteLogEx(ModuleVideo, LogError, "can not captuer dicom data");
				return -1;
			}

			WriteLogEx(ModuleVideo, LogInfo, "========== DICOM测量值文件解析start ==========");
			WriteLogEx(ModuleVideo, LogInfo, "文件路径: %s", _szDcmFilePathUtf8);

			// 3. 打印基本文件信息
			OFString sopClassUID, modality, patientName, studyDate;
			pDataset->findAndGetOFString(DCM_SOPClassUID, sopClassUID);
			pDataset->findAndGetOFString(DCM_Modality, modality);
			pDataset->findAndGetOFString(DCM_PatientName, patientName);
			pDataset->findAndGetOFString(DCM_StudyDate, studyDate);

			WriteLogEx(ModuleVideo, LogInfo, "SOP类UID:%s", sopClassUID.c_str());
			WriteLogEx(ModuleVideo, LogInfo, "mode: %s", modality.c_str());
			WriteLogEx(ModuleVideo, LogInfo, "patientName: %s", patientName.c_str());
			WriteLogEx(ModuleVideo, LogInfo, "study date : %s", studyDate.c_str());

			// 4. 解析主要内容序列
			DcmSequenceOfItems* pMainContentSeq = nullptr;
			if (pDataset->findAndGetSequence(DCM_ContentSequence, pMainContentSeq).good() && pMainContentSeq != nullptr)
			{
				unsigned long numMainItems = pMainContentSeq->card();
				WriteLogEx(ModuleVideo, LogInfo, "发现 %lu 个主要内容项", numMainItems);
				WriteLogEx(ModuleVideo, LogInfo, "---------- 测量值详细table ----------");

				// 5. 遍历主要内容项，专注于测量值数据
				int measurementCount = 0;
				for (unsigned long i = 0; i < numMainItems; i++)
				{
					DcmItem* pMainItem = pMainContentSeq->getItem(i);
					if (pMainItem)
					{
						measurementCount += this->ParseMeasurementItem(pMainItem, 0);
					}
				}

				WriteLogEx(ModuleVideo, LogInfo, "---------- all %d data ----------", measurementCount);
				nRet = 0;
			}
			else
			{
				WriteLogEx(ModuleVideo, LogError, "未找到内容序列，可能不是有效的SR文件");
				nRet = -1;
			}

			WriteLogEx(ModuleVideo, LogInfo, "========== DICOM测量值文件解析end ==========");
		}
		catch (const std::exception& e)
		{
			WriteLogEx(ModuleVideo, LogError, "解析DICOM文件异常: %s", e.what());
			nRet = -1;
		}

		return nRet;
	}

	// ParseAndPrintContentItem 辅助函数实现
	void CDcmStoreScu::ParseAndPrintContentItem(DcmItem* pItem, int level)
	{
		if (!pItem) return;

		// 1. 创建缩进字符串用于层级显示
		std::string indent(level * 2, ' ');

		// 2. 获取基本属性
		OFString relationshipType, valueType, continuityOfContent;
		pItem->findAndGetOFString(DCM_RelationshipType, relationshipType);
		pItem->findAndGetOFString(DCM_ValueType, valueType);
		pItem->findAndGetOFString(DCM_ContinuityOfContent, continuityOfContent);

		// 3. 获取概念名称
		OFString conceptCodeValue, conceptCodingScheme, conceptCodeMeaning;
		DcmSequenceOfItems* pConceptSeq = nullptr;
		if (pItem->findAndGetSequence(DCM_ConceptNameCodeSequence, pConceptSeq).good() && pConceptSeq != nullptr)
		{
			DcmItem* pConceptItem = pConceptSeq->getItem(0);
			if (pConceptItem)
			{
				pConceptItem->findAndGetOFString(DCM_CodeValue, conceptCodeValue);
				pConceptItem->findAndGetOFString(DCM_CodingSchemeDesignator, conceptCodingScheme);
				pConceptItem->findAndGetOFString(DCM_CodeMeaning, conceptCodeMeaning);
			}
		}

		// 4. 根据值类型处理不同的内容
		if (valueType == "CONTAINER")
		{
			// 容器类型 - 打印容器信息
			WriteLogEx(ModuleVideo, LogInfo, u8"%s[容器] %s (%s:%s)",
				indent.c_str(),
				conceptCodeMeaning.c_str(),
				conceptCodingScheme.c_str(),
				conceptCodeValue.c_str());

			// 递归处理子内容
			DcmSequenceOfItems* pContentSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_ContentSequence, pContentSeq).good() && pContentSeq != nullptr)
			{
				unsigned long numItems = pContentSeq->card();
				for (unsigned long i = 0; i < numItems; i++)
				{
					DcmItem* pSubItem = pContentSeq->getItem(i);
					if (pSubItem)
					{
						this->ParseAndPrintContentItem(pSubItem, level + 1);
					}
				}
			}
		}
		else if (valueType == "NUM")
		{
			// 数值类型 - 解析测量值
			OFString numericValue;
			pItem->findAndGetOFString(DCM_NumericValue, numericValue);

			// 获取单位信息
			OFString unitCodeValue, unitCodeMeaning;
			DcmSequenceOfItems* pMeasuredValueSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_MeasuredValueSequence, pMeasuredValueSeq).good() && pMeasuredValueSeq != nullptr)
			{
				DcmItem* pMeasuredItem = pMeasuredValueSeq->getItem(0);
				if (pMeasuredItem)
				{
					pMeasuredItem->findAndGetOFString(DCM_NumericValue, numericValue);

					DcmSequenceOfItems* pUnitsSeq = nullptr;
					if (pMeasuredItem->findAndGetSequence(DCM_MeasurementUnitsCodeSequence, pUnitsSeq).good() && pUnitsSeq != nullptr)
					{
						DcmItem* pUnitItem = pUnitsSeq->getItem(0);
						if (pUnitItem)
						{
							pUnitItem->findAndGetOFString(DCM_CodeValue, unitCodeValue);
							pUnitItem->findAndGetOFString(DCM_CodeMeaning, unitCodeMeaning);
						}
					}
				}
			}

			// 打印测量值信息
			WriteLogEx(ModuleVideo, LogInfo, u8"%s[测量值] %s = %s %s (%s:%s)",
				indent.c_str(),
				conceptCodeMeaning.c_str(),
				numericValue.c_str(),
				unitCodeValue.c_str(),
				conceptCodingScheme.c_str(),
				conceptCodeValue.c_str());
		}
		else if (valueType == "TEXT")
		{
			// 文本类型
			OFString textValue;
			pItem->findAndGetOFString(DCM_TextValue, textValue);

			WriteLogEx(ModuleVideo, LogInfo, u8"%s[文本] %s = \"%s\" (%s:%s)",
				indent.c_str(),
				conceptCodeMeaning.c_str(),
				textValue.c_str(),
				conceptCodingScheme.c_str(),
				conceptCodeValue.c_str());
		}
		else if (valueType == "PNAME")
		{
			// 人名类型
			OFString personName;
			pItem->findAndGetOFString(DCM_PersonName, personName);

			WriteLogEx(ModuleVideo, LogInfo, u8"%s[人名] %s = \"%s\" (%s:%s)",
				indent.c_str(),
				conceptCodeMeaning.c_str(),
				personName.c_str(),
				conceptCodingScheme.c_str(),
				conceptCodeValue.c_str());
		}
		else if (valueType == "CODE")
		{
			// 代码类型
			OFString codeValue, codingScheme, codeMeaning;
			DcmSequenceOfItems* pCodeSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_ConceptCodeSequence, pCodeSeq).good() && pCodeSeq != nullptr)
			{
				DcmItem* pCodeItem = pCodeSeq->getItem(0);
				if (pCodeItem)
				{
					pCodeItem->findAndGetOFString(DCM_CodeValue, codeValue);
					pCodeItem->findAndGetOFString(DCM_CodingSchemeDesignator, codingScheme);
					pCodeItem->findAndGetOFString(DCM_CodeMeaning, codeMeaning);
				}
			}

			WriteLogEx(ModuleVideo, LogInfo, u8"%s[代码] %s = %s (%s:%s -> %s:%s)",
				indent.c_str(),
				conceptCodeMeaning.c_str(),
				codeMeaning.c_str(),
				conceptCodingScheme.c_str(),
				conceptCodeValue.c_str(),
				codingScheme.c_str(),
				codeValue.c_str());
		}
		else if (valueType == "DATE")
		{
			// 日期类型
			OFString dateValue;
			pItem->findAndGetOFString(DCM_Date, dateValue);

			WriteLogEx(ModuleVideo, LogInfo, u8"%s[日期] %s = %s (%s:%s)",
				indent.c_str(),
				conceptCodeMeaning.c_str(),
				dateValue.c_str(),
				conceptCodingScheme.c_str(),
				conceptCodeValue.c_str());
		}
		else
		{
			// 其他类型
			WriteLogEx(ModuleVideo, LogInfo, u8"%s[%s] %s (%s:%s)",
				indent.c_str(),
				valueType.c_str(),
				conceptCodeMeaning.c_str(),
				conceptCodingScheme.c_str(),
				conceptCodeValue.c_str());
		}
	}

	// ExtractAndPrintMeasurements 专门提取和打印测量值的函数
	int CDcmStoreScu::ExtractAndPrintMeasurements(DcmItem* pItem, int level)
	{
		if (!pItem) return 0;

		int measurementCount = 0;

		// 1. 获取基本属性
		OFString valueType;
		pItem->findAndGetOFString(DCM_ValueType, valueType);

		// 2. 如果是数值类型，直接处理
		if (valueType == "NUM")
		{
			// 获取概念名称（测量值名称）
			OFString conceptCodeMeaning;
			DcmSequenceOfItems* pConceptSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_ConceptNameCodeSequence, pConceptSeq).good() && pConceptSeq != nullptr)
			{
				DcmItem* pConceptItem = pConceptSeq->getItem(0);
				if (pConceptItem)
				{
					pConceptItem->findAndGetOFString(DCM_CodeMeaning, conceptCodeMeaning);
				}
			}

			// 获取测量值和单位
			OFString numericValue, unitCode, unitMeaning;
			DcmSequenceOfItems* pMeasuredValueSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_MeasuredValueSequence, pMeasuredValueSeq).good() && pMeasuredValueSeq != nullptr)
			{
				DcmItem* pMeasuredItem = pMeasuredValueSeq->getItem(0);
				if (pMeasuredItem)
				{
					pMeasuredItem->findAndGetOFString(DCM_NumericValue, numericValue);

					// 获取单位信息
					DcmSequenceOfItems* pUnitsSeq = nullptr;
					if (pMeasuredItem->findAndGetSequence(DCM_MeasurementUnitsCodeSequence, pUnitsSeq).good() && pUnitsSeq != nullptr)
					{
						DcmItem* pUnitItem = pUnitsSeq->getItem(0);
						if (pUnitItem)
						{
							pUnitItem->findAndGetOFString(DCM_CodeValue, unitCode);
							pUnitItem->findAndGetOFString(DCM_CodeMeaning, unitMeaning);
						}
					}
				}
			}

			// 打印测量值信息（格式：测量值名称 = 数值 单位）
			if (!conceptCodeMeaning.empty() && !numericValue.empty())
			{
				std::string unitDisplay = unitCode.empty() ? "" : unitCode.c_str();
				if (unitDisplay.empty() && !unitMeaning.empty())
				{
					unitDisplay = unitMeaning.c_str();
				}

				WriteLogEx(ModuleVideo, LogInfo, u8"测量值: %s = %s %s",
					conceptCodeMeaning.c_str(),
					numericValue.c_str(),
					unitDisplay.c_str());

				measurementCount++;
			}
		}
		else if (valueType == "CONTAINER")
		{
			// 3. 如果是容器类型，递归处理子内容
			DcmSequenceOfItems* pContentSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_ContentSequence, pContentSeq).good() && pContentSeq != nullptr)
			{
				unsigned long numItems = pContentSeq->card();
				for (unsigned long i = 0; i < numItems; i++)
				{
					DcmItem* pSubItem = pContentSeq->getItem(i);
					if (pSubItem)
					{
						measurementCount += this->ExtractAndPrintMeasurements(pSubItem, level + 1);
					}
				}
			}
		}
		// 4. 其他类型（TEXT, PNAME, CODE, DATE等）不是测量值，跳过

		return measurementCount;
	}
	//	添加测量值
	void CDcmStoreScu::AddMeasurement(DcmSequenceOfItems* _pSeq, const char* _szCodeInfo, const char* _szCodingScheme,
		const char* _szCodeMeaning, const char* _szCodeValue, const char* _szCodeUnit)
	{
		DcmItem* pMeasurItem = new DcmItem();

		// 创建概念名称代码序列 
		DcmSequenceOfItems* pConceptSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
		DcmItem *codeItem = new DcmItem();
		codeItem->putAndInsertString(DCM_CodeValue, _szCodeInfo);
		codeItem->putAndInsertString(DCM_CodingSchemeDesignator, _szCodingScheme);
		codeItem->putAndInsertString(DCM_CodeMeaning, _szCodeMeaning);
		pConceptSeq->insert(codeItem);
		pMeasurItem->insert(pConceptSeq);

		// 创建测量值序列 
		DcmSequenceOfItems* pMeasSeq = new DcmSequenceOfItems(DCM_MeasuredValueSequence);
		DcmItem *measItem = new DcmItem();

		// 设置数值 
		measItem->putAndInsertString(DCM_NumericValue, _szCodeValue);

		// 设置单位代码序列 
		DcmSequenceOfItems *unitSeq = new DcmSequenceOfItems(DCM_MeasurementUnitsCodeSequence);
		DcmItem *unitItem = new DcmItem();
		unitItem->putAndInsertString(DCM_CodeValue, _szCodeUnit);
		unitItem->putAndInsertString(DCM_CodingSchemeDesignator, "UCUM");
		unitItem->putAndInsertString(DCM_CodingSchemeVersion, _szCodeValue);
		//unitItem->putAndInsertString(DCM_CodeMeaning, (strcmp(_szCodeUnit, "mm") == 0) ? "millimeter" : "centimeter");
		unitItem->putAndInsertString(DCM_CodeMeaning, _szCodeUnit);
		unitSeq->insert(unitItem);
		measItem->insert(unitSeq);

		pMeasSeq->insert(measItem);
		pMeasurItem->insert(pMeasSeq);

		_pSeq->insert(pMeasurItem);
	}
	
	bool CDcmStoreScu::ParseMeasurementDcmFile(const char* _szInputDcmFile)
	{
		bool bRet = false;
		if (_szInputDcmFile == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, u8"输入文件路径为空");
			return bRet;
		}

		try {
			// 1. 加载DICOM文件
			DcmFileFormat fileformat;
			OFCondition status = fileformat.loadFile(_szInputDcmFile);
			if (status.bad())
			{
				WriteLogEx(ModuleVideo, LogError, u8"无法打开DICOM文件: %s", _szInputDcmFile);
				return bRet;
			}

			// 2. 获取数据集
			DcmDataset* pDataset = fileformat.getDataset();
			if (!pDataset)
			{
				WriteLogEx(ModuleVideo, LogError, u8"can not capture DICO data set");
				return bRet;
			}

			WriteLogEx(ModuleVideo, LogInfo, u8"开始解析DICOM测量值文件: %s", _szInputDcmFile);

			// 3. 验证是否为SR文件
			OFString sopClassUID, modality;
			pDataset->findAndGetOFString(DCM_SOPClassUID, sopClassUID);
			pDataset->findAndGetOFString(DCM_Modality, modality);

			if (modality != "SR")
			{
				WriteLogEx(ModuleVideo, LogWarning, u8"警告: 文件模态不是SR，可能不包含结构化测量值--");
			}

			// 4. 获取主要内容序列
			DcmSequenceOfItems* pMainContentSeq = nullptr;
			if (pDataset->findAndGetSequence(DCM_ContentSequence, pMainContentSeq).good() && pMainContentSeq != nullptr)
			{
				unsigned long numMainItems = pMainContentSeq->card();
				WriteLogEx(ModuleVideo, LogInfo, u8"发现 %lu 个主要内容项", numMainItems);

				// 5. 递归解析所有内容项，查找测量值
				int totalMeasurements = 0;
				for (unsigned long i = 0; i < numMainItems; i++)
				{
					DcmItem* pMainItem = pMainContentSeq->getItem(i);
					if (pMainItem)
					{
						totalMeasurements += this->ParseMeasurementItem(pMainItem, 0);
					}
				}

				WriteLogEx(ModuleVideo, LogInfo, u8"解析完成，共找到 %d 个测量值 ", totalMeasurements);
				bRet = (totalMeasurements > 0);
			}
			else
			{
				WriteLogEx(ModuleVideo, LogError, u8"未找到内容序列，可能不是有效的SR文件");
				bRet = false;
			}
		}
		catch (const std::exception& e)
		{
			WriteLogEx(ModuleVideo, LogError, u8"解析DICOM文件异常: %s", e.what());
			bRet = false;
		}

		return bRet;
	}

	// ParseMeasurementItem 递归解析测量项的辅助函数
	int CDcmStoreScu::ParseMeasurementItem(DcmItem* pItem, int level)
	{
		if (!pItem) return 0;

		int measurementCount = 0;

		// 1. 获取基本属性
		OFString relationshipType, valueType, continuityOfContent;
		pItem->findAndGetOFString(DCM_RelationshipType, relationshipType);
		pItem->findAndGetOFString(DCM_ValueType, valueType);
		pItem->findAndGetOFString(DCM_ContinuityOfContent, continuityOfContent);

		// 2. 获取概念名称
		OFString conceptCodeValue, conceptCodingScheme, conceptCodeMeaning;
		DcmSequenceOfItems* pConceptSeq = nullptr;
		if (pItem->findAndGetSequence(DCM_ConceptNameCodeSequence, pConceptSeq).good() && pConceptSeq != nullptr)
		{
			DcmItem* pConceptItem = pConceptSeq->getItem(0);
			if (pConceptItem)
			{
				pConceptItem->findAndGetOFString(DCM_CodeValue, conceptCodeValue);
				pConceptItem->findAndGetOFString(DCM_CodingSchemeDesignator, conceptCodingScheme);
				pConceptItem->findAndGetOFString(DCM_CodeMeaning, conceptCodeMeaning);
			}
		}

		// 3. 处理不同的值类型
		if (valueType == "NUM")
		{
			// 数值类型 - 这是真正的测量值
			OFString numericValue, unitCode, unitMeaning;

			// 从MeasuredValueSequence中获取数值和单位
			DcmSequenceOfItems* pMeasuredValueSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_MeasuredValueSequence, pMeasuredValueSeq).good() && pMeasuredValueSeq != nullptr)
			{
				DcmItem* pMeasuredItem = pMeasuredValueSeq->getItem(0);
				if (pMeasuredItem)
				{
					// 获取数值
					pMeasuredItem->findAndGetOFString(DCM_NumericValue, numericValue);

					// 获取单位信息
					DcmSequenceOfItems* pUnitsSeq = nullptr;
					if (pMeasuredItem->findAndGetSequence(DCM_MeasurementUnitsCodeSequence, pUnitsSeq).good() && pUnitsSeq != nullptr)
					{
						DcmItem* pUnitItem = pUnitsSeq->getItem(0);
						if (pUnitItem)
						{
							pUnitItem->findAndGetOFString(DCM_CodeValue, unitCode);
							pUnitItem->findAndGetOFString(DCM_CodeMeaning, unitMeaning);
						}
					}
				}
			}

			// 打印测量值信息
			if (!conceptCodeMeaning.empty() && !numericValue.empty())
			{
				std::string unitDisplay = unitCode.empty() ? "" : unitCode.c_str();
				if (unitDisplay.empty() && !unitMeaning.empty())
				{
					unitDisplay = unitMeaning.c_str();
				}

				WriteLogEx(ModuleVideo, LogDebug, "pase mer info: %s = %s %s (代码: %s:%s)",
					conceptCodeMeaning.c_str(),
					numericValue.c_str(),
					unitDisplay.c_str(),
					conceptCodingScheme.c_str(),
					conceptCodeValue.c_str());

				measurementCount++;
			}
		}
		else if (valueType == "CONTAINER")
		{
			// 递归处理子内容序列
			DcmSequenceOfItems* pContentSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_ContentSequence, pContentSeq).good() && pContentSeq != nullptr)
			{
				unsigned long numItems = pContentSeq->card();
				if (numItems > 0) {
					// 容器类型 - 递归处理子内容
					WriteLogEx(ModuleVideo, LogDebug, "解析容器:%s (%s:%s)",
						conceptCodeMeaning.c_str(),
						conceptCodingScheme.c_str(),
						conceptCodeValue.c_str());


					for (unsigned long i = 0; i < numItems; i++)
					{
						DcmItem* pSubItem = pContentSeq->getItem(i);
						if (pSubItem)
						{
							measurementCount += this->ParseMeasurementItem(pSubItem, level + 1);
						}
					}
				}
			}
		}
		/*else if (valueType == "TEXT")
		{
			// 文本类型
			OFString textValue;
			pItem->findAndGetOFString(DCM_TextValue, textValue);

			WriteLogEx(ModuleVideo, LogDebug, "parse text: %s = \"%s\"",
				conceptCodeMeaning.c_str(),
				textValue.c_str());
		}
		else if (valueType == "PNAME")
		{
			// 人名类型
			OFString personName;
			pItem->findAndGetOFString(DCM_PersonName, personName);

			WriteLogEx(ModuleVideo, LogDebug, "parse name: %s = \"%s\"",
				conceptCodeMeaning.c_str(),
				personName.c_str());
		}
		else if (valueType == "CODE")
		{
			// 代码类型
			OFString codeValue, codingScheme, codeMeaning;
			DcmSequenceOfItems* pCodeSeq = nullptr;
			if (pItem->findAndGetSequence(DCM_ConceptCodeSequence, pCodeSeq).good() && pCodeSeq != nullptr)
			{
				DcmItem* pCodeItem = pCodeSeq->getItem(0);
				if (pCodeItem)
				{
					pCodeItem->findAndGetOFString(DCM_CodeValue, codeValue);
					pCodeItem->findAndGetOFString(DCM_CodingSchemeDesignator, codingScheme);
					pCodeItem->findAndGetOFString(DCM_CodeMeaning, codeMeaning);
				}
			}

			WriteLogEx(ModuleVideo, LogDebug, "parse code: %s = %s (%s:%s)",
				conceptCodeMeaning.c_str(),
				codeMeaning.c_str(),
				codingScheme.c_str(),
				codeValue.c_str());
		}
		else if (valueType == "DATE")
		{
			// 日期类型
			OFString dateValue;
			pItem->findAndGetOFString(DCM_Date, dateValue);

			WriteLogEx(ModuleVideo, LogDebug, "parse date:%s = %s",
				conceptCodeMeaning.c_str(),
				dateValue.c_str());
		}*/

		return measurementCount;
	}

	// CreateMeasuredDcmFileEx 优化后的辅助函数实现

	// 1. 创建单个测量项
	DcmItem* CDcmStoreScu::CreateMeasurementItem(const tagMeasurementData& measurement)
	{
		DcmItem* pMeasurementItem = new DcmItem();

		// 设置关系类型和值类型
		pMeasurementItem->putAndInsertString(DCM_RelationshipType, "CONTAINS");
		pMeasurementItem->putAndInsertString(DCM_ValueType, "NUM");

		// 创建概念名称代码序列
		DcmSequenceOfItems* pConceptSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
		DcmItem* pConceptItem = new DcmItem();
		pConceptItem->putAndInsertString(DCM_CodeValue, measurement.strCodeValue.c_str());
		pConceptItem->putAndInsertString(DCM_CodingSchemeDesignator, measurement.strCodingScheme.c_str());
		pConceptItem->putAndInsertString(DCM_CodeMeaning, measurement.strCodeMeaning.c_str());
		pConceptSeq->insert(pConceptItem);
		pMeasurementItem->insert(pConceptSeq);

		// 创建测量值序列
		DcmSequenceOfItems* pMeasuredValueSeq = new DcmSequenceOfItems(DCM_MeasuredValueSequence);
		DcmItem* pMeasuredItem = new DcmItem();

		// 设置数值
		pMeasuredItem->putAndInsertString(DCM_NumericValue, measurement.strNumericValue.c_str());

		// 创建测量单位代码序列
		DcmSequenceOfItems* pUnitsSeq = new DcmSequenceOfItems(DCM_MeasurementUnitsCodeSequence);
		DcmItem* pUnitItem = new DcmItem();
		pUnitItem->putAndInsertString(DCM_CodeValue, measurement.strUnitCode.c_str());
		pUnitItem->putAndInsertString(DCM_CodingSchemeDesignator, "UCUM");
		pUnitItem->putAndInsertString(DCM_CodingSchemeVersion, "1.4");
		pUnitItem->putAndInsertString(DCM_CodeMeaning, measurement.strUnitMeaning.c_str());
		pUnitsSeq->insert(pUnitItem);
		pMeasuredItem->insert(pUnitsSeq);

		pMeasuredValueSeq->insert(pMeasuredItem);
		pMeasurementItem->insert(pMeasuredValueSeq);

		WriteLogEx(ModuleVideo, LogDebug, "create measurement item :%s=%s---%s",
			measurement.strCodeMeaning.c_str(),
			measurement.strNumericValue.c_str(),
			measurement.strUnitCode.c_str());

		return pMeasurementItem;
	}

	// 2. 创建生物测量容器
	DcmItem* CDcmStoreScu::CreateBiometryContainer(const std::vector<tagMeasurementData>& measurements)
	{
		if (measurements.size() <= 0)
			return nullptr;

		DcmItem* pContainer = new DcmItem();

		// 设置容器属性
		pContainer->putAndInsertString(DCM_RelationshipType, "CONTAINS");
		pContainer->putAndInsertString(DCM_ValueType, "CONTAINER");
		pContainer->putAndInsertString(DCM_ContinuityOfContent, "SEPARATE");

		// 创建概念名称代码序列
		DcmSequenceOfItems* pConceptSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
		DcmItem* pConceptItem = new DcmItem();
		pConceptItem->putAndInsertString(DCM_CodeValue, "125002");
		pConceptItem->putAndInsertString(DCM_CodingSchemeDesignator, "DCM");
		pConceptItem->putAndInsertString(DCM_CodeMeaning, "Fetal Biometry");
		pConceptSeq->insert(pConceptItem);
		pContainer->insert(pConceptSeq);

		// 创建内容序列
		DcmSequenceOfItems* pContentSeq = new DcmSequenceOfItems(DCM_ContentSequence);

		// 添加胎儿ID
		DcmItem* pFetusIdItem = new DcmItem();
		pFetusIdItem->putAndInsertString(DCM_RelationshipType, "HAS OBS CONTEXT");
		pFetusIdItem->putAndInsertString(DCM_ValueType, "TEXT");

		DcmSequenceOfItems* pIdConceptSeq = new DcmSequenceOfItems(DCM_ConceptNameCodeSequence);
		DcmItem* pIdConceptItem = new DcmItem();
		pIdConceptItem->putAndInsertString(DCM_CodeValue, "11951-1");
		pIdConceptItem->putAndInsertString(DCM_CodingSchemeDesignator, "LN");
		pIdConceptItem->putAndInsertString(DCM_CodeMeaning, "Fetus ID");
		pIdConceptSeq->insert(pIdConceptItem);
		pFetusIdItem->insert(pIdConceptSeq);

		pFetusIdItem->putAndInsertString(DCM_TextValue, "1");
		pContentSeq->insert(pFetusIdItem);

		// 添加所有测量值
		for (const auto& measurement : measurements)
		{
			DcmItem* pMeasurementItem = CreateMeasurementItem(measurement);
			if (pMeasurementItem)
			{
				pContentSeq->insert(pMeasurementItem);
			}
		}

		pContainer->insert(pContentSeq);

		WriteLogEx(ModuleVideo, LogInfo, "create measurements container, %d measurements", measurements.size());

		return pContainer;
	}
	
	//	修改测量值文件数据
	bool CDcmStoreScu::ModifyMeasurementDcmFile(const char* _szInputDcmFile, const char* _szInputDcmFileOut, tagStoreMeasuredInfo& _objInfo)
	{
		bool bRet = false;
		if (_szInputDcmFile == nullptr)
			return bRet;

		DcmFileFormat fileformat;
		OFCondition status = fileformat.loadFile(_szInputDcmFile);
		if (status.bad())
		{
			//WriteLogEx(ModuleVideo, LogError, u8"无法打开DICOM文件");
			return bRet;
		}

		DcmDataset *pDataset = fileformat.getDataset();
		if (!pDataset)
		{
			//WriteLogEx(ModuleVideo, LogError, u8"无法获取数据集");
			return bRet;
		}

		char sopInstanceUID[100] = {};
		dcmGenerateUniqueIdentifier(sopInstanceUID, SITE_INSTANCE_UID_ROOT);
		pDataset->putAndInsertString(DCM_SOPInstanceUID, sopInstanceUID);
		
		char studyUID[100] = {}, seriesUID                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        [100] = {};
		dcmGenerateUniqueIdentifier(studyUID, SITE_STUDY_UID_ROOT);
		dcmGenerateUniqueIdentifier(seriesUID, SITE_SERIES_UID_ROOT);
		//pDataset->putAndInsertString(DCM_ValueType, "CONTAINER");

		pDataset->putAndInsertString(DCM_StudyInstanceUID, studyUID);
		pDataset->putAndInsertString(DCM_SeriesInstanceUID, seriesUID);

		// 设置患者信息 
		pDataset->putAndInsertString(DCM_PatientName, _objInfo.strPatientName.c_str());
		pDataset->putAndInsertString(DCM_PatientID, _objInfo.strPatientId.c_str());
		pDataset->putAndInsertString(DCM_AccessionNumber, _objInfo.strAccessionNumber.c_str());
		// 设置日期和时间 
		pDataset->putAndInsertString(DCM_StudyDate, _objInfo.strStudyDate.c_str());
		
		pDataset->putAndInsertString(DCM_ContentDate, _objInfo.strStudyDate.c_str());
		pDataset->putAndInsertString(DCM_SeriesDate, _objInfo.strStudyDate.c_str());
		pDataset->putAndInsertString(DCM_InstanceCreationDate, _objInfo.strStudyDate.c_str());
		//pDataset->putAndInsertString(DCM_ContentTime, "120000");
		//pDataset->putAndInsertString(DCM_StudyTime, "120000");
		// 保存文件 
		status = fileformat.saveFile(LPM::U2AEx(_szInputDcmFileOut).c_str());
		if (status.bad()) {
			WriteLogEx(ModuleVideo, LogError, u8"Error saving DICOM file: %s", LPM::W2U(LPM::A2WEx(status.text()).c_str()).c_str());
		}
	
		return true;
	}
	void CDcmStoreScu::StoreScuCallback(void* callbackData, T_DIMSE_StoreProgress* _pProgress, T_DIMSE_C_StoreRQ* _pReq)
	{
		if (_pProgress->state == DIMSE_StoreBegin)
		{
			OFString str;
			//OFLOG_DEBUG(storescuLogger, DIMSE_dumpMessage(str, *req, DIMSE_OUTGOING));
		}

		// We can't use oflog for the pdu output, but we use a special logger for
		// generating this output. If it is set to level "INFO" we generate the
		// output, if it's set to "DEBUG" then we'll assume that there is debug output
		// generated for each PDU elsewhere.
		/*OFLogger progressLogger = OFLog::getLogger("dcmtk.apps." OFFIS_CONSOLE_APPLICATION ".progress");
		if (progressLogger.getChainedLogLevel() == OFLogger::INFO_LOG_LEVEL) {
			switch (progress->state) {
			case DIMSE_StoreBegin:
				COUT << "XMIT: "; break;
			case DIMSE_StoreEnd:
				COUT << OFendl; break;
			default:
				COUT << "."; break;
			}
			COUT.flush();
		}*/
	}
}








