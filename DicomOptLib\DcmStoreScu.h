#pragma once

#include "DcmInterface.h"

#include <string>

struct T_ASC_Association;
struct T_DIMSE_StoreProgress;
struct T_DIMSE_C_StoreRQ;
struct T_ASC_Network;
struct T_ASC_Parameters;
struct T_ASC_Association;
class  DcmSequenceOfItems;
class  DcmFileFormat;
class  DcmItem;



namespace LPM
{
	class LPM_DCM_OPT_API CDcmStoreScu
	{
	public:

		CDcmStoreScu();
		~CDcmStoreScu();

		//	初始化网络, 远程的title 要一致， 不然连接不上
		int InitParam(const char* _szRemoteIp, const int _nRemotePort, const char* _szRemoteTitle, const char* _szLocalTitle);

		//	连接网络
		int ConnectNet();

		//	断开网络
		int DisconnectNet();
	
		//	储存dcm 文件
		int SendFileStoreScu(const char* _szDcmFilePath, const tagPatientShotPicInfo& _objInfo, bool _bIsGeServer = false);	//	发送dicom文件给scp

		int SendFileStoreScu(const char* _szPicFilePath,bool _bJpgOrBmp, const tagPatientShotPicInfo& _objInfo);	//	发送jpg 或者 bmp 文件给scp
		int SendFileStoreScuGe(const char* _szPicFilePath, bool _bJpgOrBmp, const tagPatientShotPicInfo& _objInfo);	//	发送jpg 或者 bmp 文件给scp, GE版本 

		int SendFileStoreScu(const char* _szImageData, int _nDataLength, bool _bJpgOrBmp, const tagPatientShotPicInfo& _objInfo);//	发送图形内存数据给scp

		//	发送测量值给工作站
		int SendMeasuredScu(const tagStoreMeasuredInfo& _objInfo);

		//	生成一个dicom 测量值文件
		int CreateMeasuredDcmFile(const char* _szSavecmFilePathUtf8, tagStoreMeasuredInfo& _objInfo);
		int CreateMeasuredDcmFileEx(const char* _szSavecmFilePathUtf8);
		int PrintMeasuredInfoToLogFile(const char* _szDcmFilePathUtf8);
		//	修改测量值文件数据
		bool ModifyMeasurementDcmFile(const char* _szInputDcmFile, const char* _szInputDcmFileOut, tagStoreMeasuredInfo& _objInfo);
	private:

		int CreateNet();
		int CraateParam();
		int CreateAssoc();

		//	添加测量值
		void AddMeasurement(DcmSequenceOfItems* _pSeq, const char* _szCodeInfo, const char* _szCodingScheme,
			const char* _szCodeMeaning, const char* _szCodeValue, const char* _szCodeUnit);

		//	解析dicom 测量值文件
		bool ParseMeasurementDcmFile(const char* _szInputDcmFile);

		// PrintMeasuredInfoToLogFile 辅助函数
		void ParseAndPrintContentItem(DcmItem* pItem, int level);
		int ExtractAndPrintMeasurements(DcmItem* pItem, int level);

		// ParseMeasurementDcmFile 辅助函数
		int ParseMeasurementItem(DcmItem* pItem, int level);


		
			

	public:

		int SendFileStoreScu(DcmFileFormat* _objFileFmt, const tagPatientShotPicInfo& _objInfo);

	protected:
		static void	StoreScuCallback(void* callbackData,T_DIMSE_StoreProgress* _pProgress,T_DIMSE_C_StoreRQ* _pReq);

	private:
		T_ASC_Network* m_pStoreScuNet = nullptr;
		T_ASC_Parameters* m_pStoreScuParams = nullptr;
		T_ASC_Association* m_pAssoc = nullptr;

		//	传输模式， 同步DIMSE_BLOCKING 还是异步 DIMSE_NONBLOCKING
		int m_nBlockingMode = 0;//0:阻塞模式， 1:异步模式
		//	远程ip
		char m_szRemoteIp[256] = {};
		//	远程端口
		int	m_nRemotePort = 0;
		//	远程名称
		char m_szRemoteTitle[256] = {};
		//	本地名称
		char m_szLocalTitle[256] = {};

		bool m_bConnectScp = false;
	};
}
/*

class ZSCFindCallback :public DcmFindSCUCallback
{
public:
	ZSCFindCallback()
	{

	}

	~ZSCFindCallback()
	{

	}

	virtual void callback(
		T_DIMSE_C_FindRQ *request,
		int &responseCount,
		T_DIMSE_C_FindRSP *rsp,
		DcmDataset *responseIdentifiers)
	{

		//std::cout<<DcmObject::PrintHelper(*responseIdentifiers);
	}

};*/


/*
C - ECHO
C - STORE
C - FIND
C - GET
C - MOVE*/

