#pragma once

#include "opencv\cv.h"
#include "opencv2\opencv.hpp"
#include "opencv\highgui.h"
#include "winsock2.h"

#include "../Depends/Common/include/LPMCommonLib.h"

#pragma comment(lib, "../Depends/Common/lib/LPMCommonLib.lib")

#ifdef _DEBUG
#pragma comment(lib, "./OpenCv/lib/opencv_world340d.lib")
#else
#pragma comment(lib, "./OpenCv/lib/opencv_world340.lib")
#endif // DEBUG

#include "dcmtk/config/osconfig.h"
#include "dcmtk/dcmdata/dctk.h"
#include "dcmtk/dcmnet/scu.h"

#include "dcmtk/dcmdata/dcpxitem.h"
#include "dcmtk/config/osconfig.h"
#include "dcmtk/dcmdata/cmdlnarg.h"
#include "dcmtk/ofstd/ofconapp.h"
#include "dcmtk/dcmdata/dcuid.h"
#include "dcmtk/dcmdata/dcfilefo.h"
#include "dcmtk/dcmdata/dcdict.h"
#include "dcmtk/dcmdata/libi2d/i2d.h"
#include "dcmtk/dcmdata/libi2d/i2djpgs.h"
#include "dcmtk/dcmdata/libi2d/i2dbmps.h"
#include "dcmtk/dcmdata/libi2d/i2dplsc.h"
#include "dcmtk/dcmdata/libi2d/i2dplvlp.h"
#include "dcmtk/dcmdata/libi2d/i2dplnsc.h"
#include "dcmtk/dcmdata/dcrledrg.h"
#include "dcmtk/dcmjpeg/djdecode.h"
#include "dcmtk/dcmimgle/dcmimage.h"
#include "dcmtk/dcmjpeg/dipijpeg.h"
#include "dcmtk/dcmtls/tlslayer.h"
#include "dcmtk/dcmtls/tlsopt.h"
#include "dcmtk/dcmnet/dfindscu.h"
#include "dcmtk/dcmnet/diutil.h"
#include "dcmtk/dcmwlm/wldsfs.h"
#include "dcmtk/dcmjpeg/djdecode.h"  /* for JPEG decoders */
#include "dcmtk/dcmjpeg/djencode.h"  /* for JPEG encoders */
#include "dcmtk/dcmnet/dcmtrans.h"    /* for dcmSocketSend/ReceiveTimeout */


#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "wsock32.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "netapi32.lib")

#pragma  comment(lib, "./dcmtkLib/ofstd.lib")
#pragma  comment(lib, "./dcmtkLib/oflog.lib")
#pragma  comment(lib, "./dcmtkLib/dcmdata.lib")
#pragma  comment(lib, "./dcmtkLib/i2d.lib")
#pragma  comment(lib, "./dcmtkLib/dcmimage.lib")
#pragma  comment(lib, "./dcmtkLib/dcmjpeg.lib")
#pragma  comment(lib, "./dcmtkLib/dcmnet.lib")
#pragma  comment(lib, "./dcmtkLib/dcmwlm.lib")

#pragma  comment(lib, "./dcmtkLib/dcmtls.lib")
#pragma  comment(lib, "./dcmtkLib/dcmjpls.lib")
#pragma  comment(lib, "./dcmtkLib/dcmimgle.lib")
//#pragma  comment(lib, "./lib/dcmimage.lib")
//#pragma  comment(lib, "./dcmtkLib/zlib_d.lib")
/*#pragma  comment(lib, "./lib/dcmrt.lib")
#pragma  comment(lib, "./lib/dcmpmap.lib")

#pragma  comment(lib, "./lib/dcmimage.lib")
#pragma  comment(lib, "./lib/dcmtkcharls.lib")
#pragma  comment(lib, "./lib/dcmdata.lib")
#pragma  comment(lib, "./lib/dcmdata.lib")
#pragma  comment(lib, "./lib/dcmdata.lib")
#pragma  comment(lib, "./lib/dcmdata.lib")*/
//#pragma  comment(lib, "./lib/dcmimgle.lib")
//#pragma  comment(lib, "./lib/dcmimage.lib")
//#pragma  comment(lib, "./lib/zlib_d.lib")
